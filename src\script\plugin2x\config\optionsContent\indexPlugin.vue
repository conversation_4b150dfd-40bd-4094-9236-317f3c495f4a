<template>
    <div class="options-content-container">
        <header class="options-content-container-header">MCP服务配置</header>
        <div class="options-content-container-search">
            <SearchBar :form-cols="searchFormCols" :form="searchForm">
                <el-button type="primary" @click="handleSearch">查询</el-button>
                <el-button
                    style="margin-left: auto"
                    type="primary"
                    icon="el-icon-plus"
                    @click="addDialogVisible = true"
                    >新增字段</el-button
                >
            </SearchBar>
        </div>
        <main class="options-content-container-main">
            <InlineCardTable
                v-loading="isLoading"
                :data="serviceTableData"
                :total="total"
                :layout="'total, prev, pager, next, sizes, jumper'"
                :pagination="pagination"
                :updateTable="getTableData"
                :cardConfig="cardConfig"
                @cardEvent="handleCardEvent"
            ></InlineCardTable>
        </main>
        <!-- 新增服务弹窗 -->
        <el-dialog
            title="新增字段"
            :visible.sync="addDialogVisible"
            width="736px"
            :close-on-click-modal="false"
            destroy-on-close
            @close="addForm = $options.data().addForm"
        >
            <SearchBar ref="addFormRef" :form-cols="addFormCols" :form="addForm">
                <template #optionContent>
                    <InputToTag
                        v-model="addForm.optionContent"
                        tagType=""
                        placeholder="输入后按Enter即可创建一个选项"
                    />
                </template>
                <template #optionList>
                    <el-popover placement="right" width="240" trigger="click">
                        <el-button
                            slot="reference"
                            type="plain"
                            style="height: 32px"
                            @click="handleAddOption"
                            >选择
                        </el-button>
                    </el-popover>
                </template>
            </SearchBar>
            <div class="dialog-footer">
                <el-button @click="addDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="handleAdd">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import SearchBar from '@/script/components/SearchBar.vue';
import InputToTag from '@/script/components/InputToTag.vue';
import InlineCardTable from '@/script/components/tables/inlineCardTable/index.vue';
import { FITMODULE, OPTIONTYPE } from '@/script/constant/optionsContent/options';
// import OptionsContent from '@/script/api/module/options-content';
console.log('FITMODULE', FITMODULE, OPTIONTYPE);

export default {
    name: 'OptionsContentConfig',
    components: {
        SearchBar,
        InputToTag,
        InlineCardTable
    },
    data() {
        return {
            isLoading: false,
            // 搜索表单
            searchFormCols: [
                [
                    {
                        type: 'el-input',
                        prop: 'fieldName',
                        label: '字段名称：',
                        labelWidth: 'max-content',
                        attrs: {
                            placeholder: '请输入',
                            clearable: true
                        },
                        rules: [],
                        span: 4,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    },
                    {
                        type: 'el-select',
                        prop: 'fitModule',
                        label: '适用模块：',
                        labelWidth: 'max-content',
                        attrs: {
                            placeholder: '请选择',
                            clearable: false
                        },
                        rules: [],
                        span: 4,
                        isDisabled: false,
                        isShow: true,
                        opts: [{ label: '全部', value: 0 }].concat(FITMODULE)
                    },
                    {
                        type: 'el-select',
                        prop: 'optionType',
                        label: '选项形式：',
                        labelWidth: 'max-content',
                        attrs: {
                            placeholder: '请选择',
                            clearable: false
                        },
                        rules: [],
                        span: 4,
                        isDisabled: false,
                        isShow: true,
                        opts: [{ label: '全部', value: 0 }].concat(OPTIONTYPE)
                    },
                    {
                        type: 'tool',
                        prop: 'tool',
                        span: 12,
                        isShow: true
                    }
                ]
            ],
            searchForm: {
                fieldName: '',
                fitModule: 0,
                optionType: 0
            },
            // 新增服务弹窗
            addDialogVisible: false,
            addFormCols: [
                [
                    {
                        type: 'el-input',
                        prop: 'fieldName',
                        label: '字段名称：',
                        labelWidth: '90px',
                        attrs: {
                            placeholder: '请输入',
                            clearable: true
                        },
                        rules: [{ required: true, message: '必填', trigger: 'blur' }],
                        span: 20,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    }
                ],
                [
                    {
                        type: 'el-select',
                        prop: 'fitModule',
                        label: '适用模块：',
                        labelWidth: '90px',
                        attrs: {
                            placeholder: '请选择',
                            clearable: false
                        },
                        rules: [{ required: true, message: '必填', trigger: 'change' }],
                        span: 20,
                        isDisabled: false,
                        isShow: true,
                        opts: FITMODULE
                    }
                ],
                [
                    {
                        type: 'el-select',
                        prop: 'optionType',
                        label: '选项形式：',
                        labelWidth: '90px',
                        attrs: {
                            placeholder: '请选择',
                            clearable: false
                        },
                        rules: [{ required: true, message: '必填', trigger: 'change' }],
                        span: 20,
                        isDisabled: false,
                        isShow: true,
                        opts: OPTIONTYPE
                    }
                ],
                [
                    {
                        type: 'el-input',
                        prop: 'defaultOption',
                        label: '默认选项：',
                        labelWidth: '90px',
                        attrs: {
                            placeholder: '请输入',
                            clearable: true
                        },
                        rules: [{ required: true, message: '必填', trigger: 'blur' }],
                        span: 20,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    }
                ],
                [
                    {
                        type: 'slot',
                        prop: 'optionContent',
                        label: '选项内容：',
                        labelWidth: '90px',
                        rules: [{ required: false, message: '必填', trigger: 'blur' }],
                        span: 20,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    },
                    {
                        type: 'slot',
                        prop: 'optionList',
                        label: '',
                        labelWidth: '',
                        rules: [],
                        span: 4,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    }
                ]
            ],
            addForm: {
                mcpName: '',
                mcpDescription: '',
                mcpVersion: '',
                comment: ''
            },
            // 服务表格数据
            serviceTableData: [],
            total: 0,
            pagination: {
                curPage: 1,
                pageSize: 10
            }
        };
    },
    created() {
        this.getTableData(this.pagination);
    },
    methods: {
        handleSearch() {
            this.pagination.curPage = 1;
            this.getTableData(this.pagination);
        },
        getTableData({ curPage = 1, pageSize = this.pagination.pageSize }) {
            const res = {
                serviceFlag: 'TRUE',
                returnCode: '000001',
                returnMsg: '操作成功',
                data: {
                    total: 22,
                    list: [
                        {
                            mcpId: 129,
                            mcpName: 'wbb',
                            mcpDescription: '1',
                            mcpVersion: '1',
                            isValid: 1,
                            isDelete: 0,
                            comment: '1',
                            createdTime: '2025-08-21 17:23:07',
                            lastUpdateTime: '2025-08-21 17:23:07',
                            resourceList: null,
                            toolList: null,
                            isChoice: null
                        },
                        {
                            mcpId: 128,
                            mcpName: 'zzz',
                            mcpDescription: '1',
                            mcpVersion: '1',
                            isValid: 1,
                            isDelete: 0,
                            comment: '1',
                            createdTime: '2025-08-20 14:39:20',
                            lastUpdateTime: '2025-08-20 14:39:20',
                            resourceList: null,
                            toolList: null,
                            isChoice: null
                        },
                        {
                            mcpId: 127,
                            mcpName: 'mcp0819test',
                            mcpDescription: '1',
                            mcpVersion: '1',
                            isValid: 1,
                            isDelete: 0,
                            comment: '1',
                            createdTime: '2025-08-19 16:38:29',
                            lastUpdateTime: '2025-08-19 16:38:29',
                            resourceList: null,
                            toolList: null,
                            isChoice: null
                        },
                        {
                            mcpId: 126,
                            mcpName: 'MCP服务-新建-0815-WBB-TEST',
                            mcpDescription: 'MCP服务-集团离线订阅管理',
                            mcpVersion: '1.0',
                            isValid: 1,
                            isDelete: 0,
                            comment: '备注备注123',
                            createdTime: '2025-08-15 14:47:55',
                            lastUpdateTime: '2025-08-15 14:47:55',
                            resourceList: null,
                            toolList: null,
                            isChoice: null
                        },
                        {
                            mcpId: 125,
                            mcpName: '测试一下',
                            mcpDescription: '采集主机利用率',
                            mcpVersion: '1',
                            isValid: 1,
                            isDelete: 0,
                            comment: '',
                            createdTime: '2025-08-12 14:07:12',
                            lastUpdateTime: '2025-08-12 14:07:12',
                            resourceList: null,
                            toolList: null,
                            isChoice: null
                        },
                        {
                            mcpId: 121,
                            mcpName: '广西',
                            mcpDescription: 'mcp',
                            mcpVersion: 'mcp',
                            isValid: 1,
                            isDelete: 0,
                            comment: '',
                            createdTime: '2025-07-30 14:15:04',
                            lastUpdateTime: '2025-07-30 14:15:04',
                            resourceList: null,
                            toolList: null,
                            isChoice: null
                        },
                        {
                            mcpId: 117,
                            mcpName: '广西作战指挥',
                            mcpDescription:
                                '提供：无线网络能力查询工具，无线网络能力查询工具，家宽网络能力查询工具，商客整体网络覆盖查询工具，区域ID查询工具\n\n',
                            mcpVersion: '1',
                            isValid: 1,
                            isDelete: 0,
                            comment: '',
                            createdTime: '2025-07-30 09:00:50',
                            lastUpdateTime: '2025-07-30 09:00:50',
                            resourceList: null,
                            toolList: null,
                            isChoice: null
                        },
                        {
                            mcpId: 116,
                            mcpName: '河南人社人群分析',
                            mcpDescription: '河南人社服务',
                            mcpVersion: '0.0.1',
                            isValid: 1,
                            isDelete: 0,
                            comment: '',
                            createdTime: '2025-07-28 14:18:06',
                            lastUpdateTime: '2025-07-28 14:18:06',
                            resourceList: null,
                            toolList: null,
                            isChoice: null
                        }
                    ],
                    pageNum: 1,
                    pageSize: 10,
                    size: 10,
                    startRow: 0,
                    endRow: 9,
                    pages: 1,
                    prePage: 0,
                    nextPage: 0,
                    isFirstPage: true,
                    isLastPage: true,
                    hasPreviousPage: false,
                    hasNextPage: false,
                    navigatePages: 8,
                    navigatepageNums: [1],
                    navigateFirstPage: 1,
                    navigateLastPage: 1
                }
            };
            res.data.list = res.data.list.concat(res.data.list);
            res.data.list = res.data.list.concat(res.data.list);
            res.data.list = res.data.list.concat(res.data.list);
            res.data.list = res.data.list.slice(0, 7);
            // res.data.list = [];
            res.data.total = res.data.list.length;
            this.serviceTableData = res.data.list
                .map((item) => {
                    return {
                        rawData: item,
                        id: item.mcpId,
                        name: item.mcpName,
                        description: item.mcpDescription,
                        isValid: item.isValid,
                        createdTime: item.createdTime,
                        lastUpdateTime: item.lastUpdateTime
                    };
                })
                .sort((a, b) => {
                    return new Date(b.lastUpdateTime) - new Date(a.lastUpdateTime);
                });
            this.total = res.data.total;
            // this.isLoading = true;
            // Config.getMCPInfoPage({
            //     pageNum: curPage,
            //     pageSize: pageSize,
            //     mcpDescription: this.searchForm.mcpDescription,
            //     mcpName: this.searchForm.mcpName,
            //     isValid: this.searchForm.isValid
            // })
            //     .then((res) => {
            //         if (res.serviceFlag === 'TRUE') {
            //             this.serviceTableData = res.data.list
            //                 .map((item) => {
            //                     return {
            //                         rawData: item,
            //                         id: item.mcpId,
            //                         name: item.mcpName,
            //                         description: item.mcpDescription,
            //                         isValid: item.isValid,
            //                         createdTime: item.createdTime,
            //                         lastUpdateTime: item.lastUpdateTime
            //                     };
            //                 })
            //                 .sort((a, b) => {
            //                     return new Date(b.lastUpdateTime) - new Date(a.lastUpdateTime);
            //                 });
            //             this.total = res.data.total;
            //         }
            //     })
            //     .finally(() => {
            //         this.isLoading = false;
            //     });
        },
        handleAdd() {
            this.$refs.addFormRef.validForm().then((valid) => {
                if (valid) {
                    Config.addMCPInfo(this.addForm).then((res) => {
                        if (res.serviceFlag === 'TRUE') {
                            this.$message.success(res.returnMsg || '新增成功');
                            this.addDialogVisible = false;
                            this.handleSearch();
                        } else {
                            this.$message.error(res.returnMsg || '新增失败');
                        }
                    });
                }
            });
        },
        handleResponse(res, successMsg, errorMsg) {
            if (res.serviceFlag === 'TRUE') {
                this.$message.success(res.returnMsg || successMsg);
                this.handleSearch();
            } else {
                this.$message.error(res.returnMsg || errorMsg);
            }
        }
    }
};
</script>

<style scoped lang="less">
.options-content-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    &-header {
        height: 1.25rem;
        margin-left: 0.5rem;
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 600;
        font-size: 1rem;
        color: rgba(0, 0, 0, 0.85);
        line-height: 1.25rem;
    }
    &-search {
        height: 3.5rem;
        background: rgba(255, 255, 255, 0.96);
        border-radius: 0.375rem;
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 0 1rem;
    }
    &-main {
        height: 0;
        flex: 1;
        display: flex;
        gap: 1rem;
    }
    .dialog-footer {
        text-align: right;
        margin-top: 32px;
    }
}
</style>
