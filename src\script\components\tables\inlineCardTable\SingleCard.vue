<template>
    <section
        class="single-card"
        :class="{
            'is-expand': isExpand,
            'is-active': isActive
        }"
        :style="defaultCardConfig.diyStyle"
        @mouseenter="isActive = true"
        @mouseleave="isActive = false"
    >
        <header class="single-card-header">
            <div class="header-left">
                <!-- 图标 -->
                <img v-if="!isHide('icon')" class="header-icon" :src="cardIcon" alt="" />
                <!-- 标题 -->
                <span v-if="!isHide('title')" class="header-title text-ellipsis"
                    >{{ cardData.name }}
                </span>
            </div>
            <div class="header-right">
                <!-- 编辑 -->
                <div v-if="!isHide('edit')" class="header-edit" @click.stop="handleEditClick">
                    <img class="header-edit-icon" src="@/img/common/icon-pen.png" alt="" />
                </div>
                <!-- 删除 -->
                <div v-if="!isHide('delete')" class="header-delete" @click.stop="handleDelete">
                    <i class="el-icon-delete"></i>
                </div>
                <!-- 生效状态 -->
                <div v-if="!isHide('validate')" class="header-validate" @click.stop>
                    <el-switch
                        v-model="isValid"
                        active-color="#1565ff"
                        inactive-color="#d6dae0"
                        @change="handleValidChange"
                    ></el-switch>
                </div>
            </div>
        </header>
        <main class="single-card-content">
            <div class="content-form-item">
                适用模块：<span class="content-form-item-value">1212天{{}}</span>
            </div>
            <div class="content-form-item">
                选项形式：<span class="content-form-item-value">1212天{{}}</span>
            </div>
            <div class="content-form-item">
                默认选项：<span class="content-form-item-value">1212天{{}}</span>
            </div>
        </main>
    </section>
</template>
<script>
export default {
    name: 'SingleCard',
    props: {
        data: {
            type: Object,
            default: () => ({})
        },
        cardConfig: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            defaultCardConfig: {
                icon: {
                    0: require('@/img/common/file-close.png'),
                    1: require('@/img/common/file-open.png')
                },
                diyStyle: {},
                readonly: false,
                allowHover: false, // 是否允许鼠标悬浮
                expandType: 'expand', // ['expand', 'link']
                editType: 'normal', // ['normal', 'page']
                noCardNav: false, // 是否不显示卡片导航
                diyContent: false, // 是否自定义内容
                headerHideItem: ['tenantKey', 'tag'], // 隐藏的头部元素 ['icon', 'title', 'updateTime', 'edit', 'cancel', 'save', 'delete', 'setting', 'tenantKey', 'validate', 'expandIcon']
                navList: [
                    {
                        name: '基础信息',
                        value: 'BaseInfo'
                    }
                ],
                navListFilterFun: null // (data, navList) => {...}
            },
            isExpand: false,
            isActive: false,
            isEdit: false,
            isValid: false,
            tagMap: {
                1: { name: '内部自增', class: 'tag-in' },
                2: { name: '外部接入', class: 'tag-out' }
            },
            nowNav: '',
            dynamicComponents: {}, // 缓存已加载的组件
            cardData: {
                id: '',
                name: '',
                description: '',
                isValid: 0,
                createdTime: '',
                lastUpdateTime: ''
            },
            cardDetail: {}
        };
    },
    computed: {
        cardIcon() {
            return this.defaultCardConfig.icon[+this.isValid];
        },
        cardNavList() {
            if (!this.defaultCardConfig.navListFilterFun) {
                return this.defaultCardConfig.navList;
            }
            const newNavList = this.defaultCardConfig.navListFilterFun(
                this.data,
                this.defaultCardConfig.navList
            );
            return newNavList.filter((item) => item.isShow !== false);
        },
        // 工具管理的特殊处理：外部工具不能进行修改、删除，生效状态改变
        toolIsOutSource() {
            return this.cardData.rawData.toolSource === 2;
        }
    },
    watch: {
        data: {
            handler(newVal) {
                this.initCard();
            },
            immediate: true,
            deep: true
        },
        isExpand: {
            handler(newVal) {
                if (newVal) {
                    this.getCardDetail();
                } else {
                    this.isEdit = false;
                }
            }
        },
        cardNavList: {
            handler(newVal) {
                if (newVal.length > 0) {
                    this.nowNav = newVal[0].value;
                }
            },
            immediate: true,
            deep: true
        }
    },
    methods: {
        // 判断给定名称的头部元素是否隐藏
        /* eslint-disable complexity */
        isHide(name) {
            // 基础隐藏判断：配置项中显式隐藏
            const baseHide = (this.defaultCardConfig.headerHideItem || []).includes(name);
            if (baseHide) return true;

            // 额外逻辑判断：按按钮类型进行细分控制
            switch (name) {
                case 'copy':
                    return !(this.isExpand || this.isActive);
                case 'edit':
                    if (this.toolIsOutSource) {
                        return true;
                    }
                    return !(this.isExpand || this.isActive) || this.isEdit;
                case 'cancel':
                case 'save':
                    return !this.isEdit || this.defaultCardConfig.editType !== 'normal';
                case 'delete':
                    return !(this.isExpand || this.isActive);
                case 'setting':
                    if (this.toolIsOutSource) {
                        return true;
                    }
                    return !(this.isExpand || this.isActive);
                case 'validate':
                    if (this.toolIsOutSource) {
                        return true;
                    }
                    return false;
                default:
                    return false;
            }
        },
        initCard() {
            this.defaultCardConfig = {
                ...this.defaultCardConfig,
                ...this.cardConfig
            };
            this.cardData = JSON.parse(JSON.stringify(this.data));
            this.isValid = this.cardData.isValid === 1;
        },
        getCardDetail() {
            this.$emit('cardEvent', {
                type: 'expand',
                name: 'getCardDetail',
                params: this.cardData,
                callback: (e) => (this.cardDetail = e)
            });
        },
        handleLinkClick() {
            this.$emit('cardEvent', {
                type: 'link',
                name: 'linkCard',
                params: this.cardData,
                callback: null
            });
        },
        handleExpandClick() {
            this.$emit('cardActive', { field: 'isExpand', value: !this.isExpand });
        },
        handleCancel() {
            this.isEdit = false;
            this.getCardDetail();
        },
        async handleSave() {
            let valid = true;
            if (!this.defaultCardConfig.diyContent && !this.defaultCardConfig.noCardNav) {
                const ref = this.$refs[`${this.nowNav}Ref-${this.cardData.id}`];
                valid = await ref.validate();
            }
            if (valid) {
                this.$emit('cardEvent', {
                    type: 'save',
                    name: 'saveCard',
                    params: this.cardDetail,
                    callback: (e) => {
                        e && (this.isEdit = false);
                    }
                });
            }
        },
        handleCopyClick() {
            this.$emit('cardEvent', {
                type: 'copy',
                name: 'copyCard',
                params: this.cardData,
                callback: null
            });
        },
        handleEditClick() {
            if (this.defaultCardConfig.editType === 'normal') {
                this.isEdit = true;
                return;
            }
            if (this.defaultCardConfig.editType === 'page') {
                this.$emit('cardEvent', {
                    type: 'edit',
                    name: 'editCard',
                    params: this.cardData,
                    callback: null
                });
                return;
            }
        },
        handleDelete() {
            const h = this.$createElement;
            this.$confirm('', {
                message: h('div', { style: 'display: flex; flex-direction: column; gap: 8px;' }, [
                    h('div', { style: 'display: flex; align-items: center; gap: 8px;' }, [
                        h('i', {
                            class: 'el-icon-warning',
                            style: 'color:#f90;font-size:24px;line-height:24px;'
                        }),
                        h('span', { class: 'mcpservice-theme confirm-title' }, '确定删除吗？')
                    ]),
                    h(
                        'p',
                        { class: 'mcpservice-theme confirm-desc', style: 'margin: 0;' },
                        '删除后将无法恢复'
                    )
                ]),
                confirmButtonText: '确定',
                cancelButtonText: '取消'
            }).then(() => {
                this.$emit('cardEvent', {
                    type: 'delete',
                    name: 'deleteCard',
                    params: this.cardData.id,
                    callback: null
                });
            });
        },
        handleSettingClick() {
            this.$emit('cardEvent', {
                type: 'setting',
                name: 'settingCard',
                params: this.cardData,
                callback: null
            });
        },
        handleTenantKeyClick() {
            this.$emit('cardEvent', {
                type: 'tenantKey',
                name: 'tenantKeyCard',
                params: this.cardData,
                callback: null
            });
        },
        handleValidChange(newVal) {
            this.isValid = !newVal;
            const h = this.$createElement;
            this.$confirm('', {
                message: h('div', { style: 'display: flex; flex-direction: column; gap: 8px;' }, [
                    h('div', { style: 'display: flex; align-items: center; gap: 8px;' }, [
                        h('i', {
                            class: 'el-icon-warning',
                            style: 'color:#f90;font-size:24px;line-height:24px;'
                        }),
                        h('span', { class: 'mcpservice-theme confirm-title' }, '确定更改状态吗？')
                    ]),
                    h(
                        'p',
                        { class: 'mcpservice-theme confirm-desc', style: 'margin: 0;' },
                        '确定后状态为：' + ((newVal && '启用') || '停用')
                    )
                ]),
                confirmButtonText: '确定',
                cancelButtonText: '取消'
            }).then(() => {
                this.$emit('cardEvent', {
                    type: 'valid',
                    name: 'validCard',
                    params: {
                        id: this.cardData.id,
                        isValid: newVal
                    },
                    callback: null
                });
            });
        }
    }
};
</script>
<style scoped lang="less">
.single-card {
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.96);
    border: 0.0625rem solid transparent;
    border-radius: 0.375rem;
    overflow: hidden;
    flex: none;
    &.is-expand,
    &.is-active {
        box-shadow: 0 0.25rem 0.5rem 0 rgba(0, 54, 159, 0.1) !important;
        border: 0.0625rem solid rgba(21, 101, 255, 0.6) !important;
    }
    &-header {
        height: 3.25rem;
        background: rgba(255, 255, 255, 0.96);
        padding: 1rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .header-left,
        .header-right {
            display: flex;
            align-items: center;
        }
        .header-left {
            gap: 0.5rem;
            .header-icon {
                width: 1.5rem;
                height: 1.5rem;
            }
            .header-title {
                font-family:
                    PingFangSC,
                    PingFang SC;
                font-weight: 600;
                font-size: 1rem;
                color: rgba(0, 0, 0, 0.85);
            }
            .header-tag {
                width: 3.75rem;
                height: 1.125rem;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 0.25rem;
                font-family:
                    PingFangSC,
                    PingFang SC;
                font-weight: 600;
                font-size: 0.75rem;
                line-height: 1.25rem;
                user-select: none;
                &.tag-in {
                    color: #0dc05c;
                    background-color: #0dc05c1a;
                    border: 0.0625rem solid #0dc05c99;
                }
                &.tag-out {
                    color: #1565ff;
                    background-color: #1565ff1a;
                    border: 0.0625rem solid #1565ff99;
                }
            }
            .header-update-time {
                font-family:
                    PingFangSC,
                    PingFang SC;
                font-weight: 400;
                font-size: 0.875rem;
                color: #ff7802;
                line-height: 1.25rem;
            }
        }
        .header-right {
            user-select: none;
            gap: 1rem;
            .header-copy {
                width: 2rem;
                height: 2rem;
                background: #f6f8fa;
                border-radius: 0.25rem;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                .el-icon-document-copy {
                    font-size: 1rem;
                    color: rgba(0, 0, 0, 0.65);
                }
                &:hover {
                    .el-icon-document-copy {
                        color: #1565ff;
                    }
                }
            }
            .header-edit {
                width: 2rem;
                height: 2rem;
                background: #f6f8fa;
                border-radius: 0.25rem;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                &-icon {
                    width: 1rem;
                    height: 1rem;
                }
            }
            .header-btn {
                margin: 0;
                &.save-btn {
                    background: #ffffff;
                    border-radius: 0.25rem;
                    border: 0.0625rem solid #1565ff99;
                    color: #1565ff;
                    &:hover {
                        background: #1565ff1a;
                    }
                }
            }
            .header-delete {
                width: 2rem;
                height: 2rem;
                background: #f6f8fa;
                border-radius: 0.25rem;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                .el-icon-delete {
                    font-size: 1rem;
                    color: rgba(0, 0, 0, 0.65);
                }
                &:hover {
                    .el-icon-delete {
                        color: #f74041;
                    }
                }
            }
            .header-setting {
                width: 2rem;
                height: 2rem;
                background: #f6f8fa;
                border-radius: 0.25rem;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                .el-icon-setting {
                    font-size: 1rem;
                    color: rgba(0, 0, 0, 0.65);
                }
                &:hover {
                    .el-icon-setting {
                        color: #1565ff;
                    }
                }
            }
            .header-tenant-key {
                height: 2rem;
                padding: 0 0.5rem;
                background: #f6f8fa;
                border-radius: 0.25rem;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 0.5rem;
                cursor: pointer;
                img {
                    width: 1rem;
                    height: 1rem;
                }
                span {
                    font-family:
                        PingFangSC,
                        PingFang SC;
                    font-weight: 400;
                    font-size: 0.875rem;
                    color: #f74041;
                }
            }
            .header-validate {
                height: 2rem;
                padding: 0 0.5rem;
                background: #f6f8fa;
                border-radius: 0.25rem;
                display: flex;
                align-items: center;
                gap: 0.5rem;
                font-family:
                    PingFangSC,
                    PingFang SC;
                font-weight: 400;
                font-size: 0.875rem;
                color: rgba(0, 0, 0, 0.65);
            }
            .header-expand-icon {
                font-size: 1rem;
                color: rgba(0, 0, 0, 0.65);
                cursor: pointer;
                transition: transform 0.3s ease-in-out;
                &.rotate180 {
                    transform: rotate(-180deg);
                }
            }
        }
    }
    &-content {
        width: 100%;
        height: 0;
        flex: 1;
        .content-form-item {
            display: flex;
            align-items: center;
            padding: 0.5rem 1rem;
            font-weight: bold;
            font-size: 0.875rem;
        }
        .content-form-item-value {
            font-weight: normal;
            color: rgba(0, 0, 0, 0.65);
        }
    }
}
</style>
