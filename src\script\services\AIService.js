/* eslint-disable max-depth */
/* eslint-disable complexity */
/**
 * AI处理服务类
 * 参考DeepSeek等成熟AI问答应用的设计模式
 * 支持流式和非流式响应、请求管理、错误处理等功能
 */
import GatewayAxios from '../common/gatewayAxios';
import { API_CONFIG, ERROR_CONFIG } from '../constant/modelTest/appConfig';
import {
    getExtendedModelConfig,
    getModelHeaders,
    getModelEndpoint,
    isModelStreamingSupported,
    transformParams,
    ResponseExtractor,
    DEFAULT_MODEL
} from '../constant/modelTest/aiModelConfig';

class AIService {
    constructor(config = {}) {
        // 获取模型配置
        const modelId = config.model || DEFAULT_MODEL;
        const modelConfig = getExtendedModelConfig(modelId);
        const endpointConfig = getModelEndpoint(modelId);

        this.config = {
            // 基础配置
            model: modelId,
            apiEndpoint: endpointConfig.endpoint,
            gateway: endpointConfig.gateway,
            timeout: API_CONFIG.timeout,
            maxRetries: API_CONFIG.retry.maxRetries,
            retryDelay: API_CONFIG.retry.retryDelay,
            streamTimeout: API_CONFIG.streaming.timeout,

            // AI模型配置
            temperature: modelConfig.defaultTemperature,
            defaultModel: modelId,
            isStreaming: modelConfig.isStreaming,

            // 请求头配置（使用模型特定的请求头）
            headers: getModelHeaders(modelId, config.headers),

            // 覆盖用户自定义配置
            ...config
        };

        // 初始化响应提取器
        this.responseExtractor = new ResponseExtractor(modelId);

        // 获取流式响应标识符配置
        this.streamIdentifiers = modelConfig.streamIdentifiers || {
            dataPrefix: 'data: ',
            doneSignal: '[DONE]'
        };

        // 请求管理
        this.requestQueue = [];
        this.currentRequest = null;
        this.abortController = null;
        this.isProcessing = false;

        // 状态管理
        this.isStreaming = false;
        this.streamBuffer = '';

        // 初始化API实例
        this.api = new GatewayAxios({
            gateWay: this.config.gateway
        });
    }

    /**
     * 发送消息到AI服务
     * @param {Object} messageData - 消息数据
     * @param {string} messageData.content - 消息内容
     * @param {string} messageData.model - 使用的模型
     * @param {Array} messageData.history - 历史消息
     * ...
     * @param {Object} options - 选项
     * @param {boolean} options.stream - 是否使用流式响应
     * @param {Function} options.onChunk - 流式响应回调
     * @param {Function} options.onComplete - 完成回调
     * @param {Function} options.onError - 错误回调
     * @returns {Promise}
     */
    async sendMessage(messageData, options = {}) {
        const {
            stream = true,
            onChunk = null,
            onComplete = null,
            onError = null
        } = options;

        // 检查是否有正在进行的请求
        if (this.isProcessing) {
            throw new Error('已有请求正在处理中，请先停止当前请求');
        }

        try {
            this.isProcessing = true;
            this.abortController = new AbortController();

            // 构建通用参数格式
            const messages = [];

            // 添加系统消息（如果有）
            if (messageData.systemMessage) {
                messages.push({
                    role: 'system',
                    content: messageData.systemMessage
                });
            }

            // 添加历史消息
            if (messageData.history && messageData.history.length > 0) {
                messageData.history.forEach(msg => {
                    messages.push({
                        role: msg.role,
                        content: msg.content
                    });
                });
            }

            // 添加当前用户消息
            messages.push({
                role: 'user',
                content: messageData.content
            });

            // 构建通用参数
            const commonParams = {
                model: messageData.model || this.config.defaultModel,
                history: messages,
                temperature: messageData.temperature || this.config.temperature,
                stream: stream,
                maxTokens: messageData.maxTokens,
                query: messageData.content, // 用于Dify等平台
                conversationId: messageData.conversationId,
                user: messageData.user,
                extraParams: messageData.extraParams || {}
            };

            // 使用模型特定的参数转换
            const requestData = transformParams(this.config.model, commonParams);

            // 调试参数转换
            // console.group('🔄 AI请求参数转换');
            // console.log('模型ID:', this.config.model);
            // console.log('通用参数:', commonParams);
            // console.log('转换后参数:', requestData);
            // console.log('请求端点:', `${this.config.gateway}${this.config.apiEndpoint}`);
            // console.groupEnd();

            // 检查模型是否支持流式响应
            const supportsStreaming = isModelStreamingSupported(this.config.model);

            if (stream && supportsStreaming) {
                return await this.handleStreamRequest(requestData, {
                    onChunk,
                    onComplete,
                    onError
                });
            }
            return await this.handleNormalRequest(requestData);

        } catch (error) {
            this.isProcessing = false;
            if (onError) {
                onError(error);
            }
            throw error;
        }
    }

    /**
     * 处理流式请求
     */
    async handleStreamRequest(requestData, callbacks) {
        const { onChunk, onComplete, onError } = callbacks;

        try {
            this.isStreaming = true;
            this.streamBuffer = '';



            // 使用fetch进行流式请求
            const response = await fetch(`${this.config.gateway}${this.config.apiEndpoint}`, {
                method: 'POST',
                headers: {
                    ...this.config.headers,
                    'Accept': 'text/event-stream'
                },
                body: JSON.stringify(requestData),
                signal: this.abortController.signal
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error('AI请求失败:', response.status, response.statusText, errorText);
                throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
            }

            const reader = response.body.getReader();
            const decoder = new TextDecoder();

            let fullResponse = '';

            let streamEnded = false;

            while (true) {
                const { done, value } = await reader.read();

                if (done) {
                    // 处理缓冲区中剩余的数据
                    if (this.streamBuffer.trim()) {
                        const remainingLines = this.streamBuffer.split('\n');
                        for (const line of remainingLines) {
                            if (line.trim() && line.startsWith(this.streamIdentifiers.dataPrefix)) {
                                const data = line.slice(this.streamIdentifiers.dataPrefix.length);

                                // 检查是否是结束信号
                                if (data.trim().includes(this.streamIdentifiers.doneSignal)) {
                                    streamEnded = true;
                                    break;
                                }

                                try {
                                    const parsed = JSON.parse(data);
                                    const content = this.responseExtractor.extractStreamContent(parsed);
                                    if (content) {
                                        fullResponse += content;
                                        if (onChunk) {
                                            onChunk(content, fullResponse);
                                        }
                                    }
                                } catch (parseError) {
                                    // 解析失败，跳过此数据块
                                }
                            }
                        }
                    }
                    break;
                }

                const chunk = decoder.decode(value, { stream: true });
                this.streamBuffer += chunk;

                // 处理SSE数据
                const lines = this.streamBuffer.split('\n');
                this.streamBuffer = lines.pop() || '';

                for (const line of lines) {
                    // 使用配置的数据前缀处理数据行
                    if (line.startsWith(this.streamIdentifiers.dataPrefix)) {
                        const data = line.slice(this.streamIdentifiers.dataPrefix.length);

                        // 检查数据内容是否是结束信号
                        if (data.trim().includes(this.streamIdentifiers.doneSignal)) {
                            streamEnded = true;
                            break;
                        }

                        try {
                            const parsed = JSON.parse(data);

                            // 使用配置化的响应提取器
                            const content = this.responseExtractor.extractStreamContent(parsed);
                            if (content) {
                                fullResponse += content;
                                if (onChunk) {
                                    onChunk(content, fullResponse);
                                }
                            }
                        } catch (parseError) {
                            // 解析失败，跳过此数据块
                        }
                    }
                }

                // 如果检测到结束信号，跳出外层循环
                if (streamEnded) {
                    break;
                }
            }

            // 确保完成回调被调用
            if (onComplete) {
                onComplete(fullResponse);
            }
            return fullResponse;

        } catch (error) {
            if (error.name === 'AbortError') {
                return null;
            }

            if (onError) {
                onError(error);
            }
            throw error;
        } finally {
            this.isStreaming = false;
            this.isProcessing = false;
            this.abortController = null;
        }
    }

    /**
     * 处理普通请求
     */
    async handleNormalRequest(requestData) {
        try {

            // 使用fetch进行普通请求，确保请求头正确传递
            const response = await fetch(`${this.config.gateway}${this.config.apiEndpoint}`, {
                method: 'POST',
                headers: this.config.headers,
                body: JSON.stringify(requestData),
                signal: this.abortController.signal
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
            }

            const data = await response.json();

            // 使用配置化的响应提取器
            const content = this.responseExtractor.extractCompleteContent(data);
            if (content) {
                return content;
            }

            throw new Error('响应格式不正确');

        } catch (error) {
            if (error.name === 'AbortError') {
                return null;
            }
            throw error;
        } finally {
            this.isProcessing = false;
            this.abortController = null;
        }
    }

    /**
     * 停止当前生成
     */
    stopGeneration() {
        if (this.abortController) {
            this.abortController.abort();
            this.abortController = null;
        }

        this.isProcessing = false;
        this.isStreaming = false;
        this.streamBuffer = '';
    }

    /**
     * 检查是否正在处理请求
     */
    isResponding() {
        return this.isProcessing;
    }

    /**
     * 检查是否正在流式响应
     */
    isStreamResponse() {
        return this.isStreaming;
    }

    /**
     * 错误处理
     */
    handleError(error) {
        // 根据错误类型返回用户友好的错误信息
        if (error.name === 'AbortError') {
            return ERROR_CONFIG.errorMessages.client;
        } else if (error.message.includes('timeout')) {
            return ERROR_CONFIG.errorMessages.timeout;
        } else if (error.message.includes('network')) {
            return ERROR_CONFIG.errorMessages.network;
        } else if (error.message.includes('HTTP 5')) {
            return ERROR_CONFIG.errorMessages.server;
        }
        return error.message || ERROR_CONFIG.errorMessages.unknown;

    }

    /**
     * 重试机制
     */
    async retryRequest(requestData, options, retryCount = 0) {
        try {
            return await this.sendMessage(requestData, options);
        } catch (error) {
            if (retryCount < this.config.maxRetries) {
                await new Promise(resolve =>
                    setTimeout(resolve, this.config.retryDelay * (retryCount + 1))
                );

                return this.retryRequest(requestData, options, retryCount + 1);
            }
            throw error;

        }
    }

    /**
     * 获取支持的模型列表
     */
    async getAvailableModels() {
        try {
            const response = await fetch(`${this.config.gateway}/v1/models`, {
                method: 'GET',
                headers: this.config.headers
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            // 适配Moonshot API的模型列表格式
            if (data.data && Array.isArray(data.data)) {
                return data.data.map(model => ({
                    id: model.id,
                    name: model.id,
                    created: model.created,
                    owned_by: model.owned_by
                }));
            }

            return [];

        } catch (error) {
            console.error('获取模型列表失败:', error);
            return [];
        }
    }

    /**
     * 销毁服务实例
     */
    destroy() {
        this.stopGeneration();
        this.requestQueue = [];
        this.currentRequest = null;
    }
}

export default AIService;
